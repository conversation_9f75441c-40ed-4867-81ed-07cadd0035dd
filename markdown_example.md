# 一级标题

## 二级标题

### 三级标题

#### 四级标题

##### 五级标题

###### 六级标题

## 文本格式

**粗体文本**
*斜体文本*
***粗斜体文本***
~~删除线文本~~

## 列表

### 无序列表
- 项目1
- 项目2
  - 子项目2.1
  - 子项目2.2
- 项目3

### 有序列表
1. 第一项
2. 第二项
   1. 子项目2.1
   2. 子项目2.2
3. 第三项

## 链接和图片

[链接文本](https://example.com)
[带标题的链接](https://example.com "链接标题")

![图片alt文本](image.jpg)
![带标题的图片](image.jpg "图片标题")

## 代码

### 行内代码
这是一段包含 `行内代码` 的文本。

### 代码块
```python
def hello_world():
    print("Hello, World!")
    return "success"
```

```javascript
function greet(name) {
    console.log(`Hello, ${name}!`);
}
```

## 引用

> 这是一个引用块
> 
> 可以包含多行内容
> 
> > 这是嵌套引用

## 表格

| 列1标题 | 列2标题 | 列3标题 |
|---------|---------|---------|
| 行1列1  | 行1列2  | 行1列3  |
| 行2列1  | 行2列2  | 行2列3  |

### 表格对齐
| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:--------:|-------:|
| 左     |    中    |     右 |
| 内容   |   内容   |   内容 |

## 分割线

---

或者

***

或者

___

## 任务列表

- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务
- [ ] 待办事项

## 转义字符

如果要显示特殊字符，使用反斜杠转义：
\* \_ \# \[ \] \( \) \` \\

## 脚注

这是一个带脚注的文本[^1]。

[^1]: 这是脚注的内容。

## HTML标签

Markdown 支持部分 HTML 标签：

<strong>HTML粗体</strong>
<em>HTML斜体</em>
<u>下划线</u>
<mark>高亮文本</mark>

## 数学公式（部分支持）

行内公式：$E = mc^2$

块级公式：
$$
\sum_{i=1}^{n} x_i = x_1 + x_2 + \cdots + x_n
$$